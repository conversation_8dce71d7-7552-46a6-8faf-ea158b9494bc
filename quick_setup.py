#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradingAgents-CN 快速配置工具
帮助用户快速完成项目配置
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def print_banner():
    """打印欢迎横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                TradingAgents-CN 快速配置工具                 ║
║                                                              ║
║  🚀 基于多智能体大语言模型的中文金融交易决策框架              ║
║  🎯 专为中文用户优化，支持A股/港股/美股分析                   ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 检查Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 10:
        print("✅ Python版本符合要求")
        return True
    else:
        print("❌ Python版本过低，需要3.10或更高版本")
        print("💡 请升级Python版本后重试")
        return False

def setup_env_file():
    """设置环境变量文件"""
    print("\n📝 配置环境变量文件...")
    
    env_path = Path('.env')
    env_example_path = Path('.env.example')
    
    if env_path.exists():
        print("✅ .env文件已存在")
        return True
    
    if env_example_path.exists():
        try:
            shutil.copy(env_example_path, env_path)
            print("✅ 已从.env.example创建.env文件")
            print("💡 请编辑.env文件，填入您的API密钥")
            return True
        except Exception as e:
            print(f"❌ 创建.env文件失败: {e}")
            return False
    else:
        print("❌ .env.example文件不存在")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建项目目录...")
    
    directories = [
        'data',
        'data/cache',
        'data/reports',
        'results',
        'cache',
        'logs'
    ]
    
    for dir_path in directories:
        path = Path(dir_path)
        try:
            path.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {dir_path}")
        except Exception as e:
            print(f"❌ 创建目录失败 {dir_path}: {e}")
            return False
    
    return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装Python依赖包...")
    
    requirements_path = Path('requirements.txt')
    if not requirements_path.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    try:
        print("正在安装依赖包，请稍候...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功")
            return True
        else:
            print(f"❌ 依赖包安装失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ 安装超时，请检查网络连接")
        return False
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "="*60)
    print("🎉 基础配置完成！")
    print("\n📋 后续步骤:")
    print("1. 📝 编辑 .env 文件，填入您的API密钥:")
    print("   - 至少需要一个AI模型API密钥 (推荐DeepSeek或阿里百炼)")
    print("   - 推荐配置Tushare Token (A股数据)")
    print("   - 推荐配置FinnHub API Key (美股数据)")
    print()
    print("2. 🔍 验证配置:")
    print("   python check_config.py")
    print()
    print("3. 🚀 启动Web界面:")
    print("   python start_web.py")
    print()
    print("4. 🌐 访问应用:")
    print("   http://localhost:8501")
    print()
    print("📚 详细文档: README.md")
    print("❓ 遇到问题: docs/faq/faq.md")
    print("="*60)

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 设置环境文件
    if not setup_env_file():
        return False
    
    # 创建目录
    if not create_directories():
        return False
    
    # 询问是否安装依赖
    print("\n❓ 是否安装Python依赖包? (y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是', '']:
            if not install_dependencies():
                print("⚠️  依赖包安装失败，请手动运行: pip install -r requirements.txt")
        else:
            print("⏭️  跳过依赖包安装")
    except KeyboardInterrupt:
        print("\n\n👋 配置已取消")
        return False
    
    # 显示后续步骤
    show_next_steps()
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 配置已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 配置过程出错: {e}")
        sys.exit(1)
