#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradingAgents-CN 简单启动脚本
仅安装核心依赖，快速启动应用
"""

import subprocess
import sys
import os

def install_core_packages():
    """安装核心依赖包"""
    core_packages = [
        'streamlit',
        'plotly', 
        'pandas',
        'requests',
        'python-dotenv'
    ]
    
    print("🚀 正在安装核心依赖包...")
    for package in core_packages:
        try:
            print(f"📦 安装 {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
            return False
    
    return True

def start_streamlit():
    """启动Streamlit应用"""
    print("\n🚀 启动TradingAgents-CN...")
    print("💡 启动后请访问: http://localhost:8501")
    print("💡 按 Ctrl+C 可以停止应用")
    
    try:
        # 检查是否存在start_web.py
        if os.path.exists('start_web.py'):
            subprocess.run([sys.executable, 'start_web.py'])
        else:
            print("❌ 找不到start_web.py文件")
            return False
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 TradingAgents-CN 简单启动")
    print("=" * 40)
    
    # 安装核心依赖
    if not install_core_packages():
        print("❌ 依赖安装失败")
        return
    
    print("\n✅ 核心依赖安装完成")
    
    # 启动应用
    start_streamlit()

if __name__ == "__main__":
    main()
