# TradingAgents-CN 配置指南

## 🚀 快速开始

### 1. 环境要求
- Python 3.10+ 
- 4GB+ RAM (推荐 8GB+)
- 稳定的网络连接

### 2. 配置步骤

#### 步骤1: 配置API密钥
编辑 `.env` 文件，填入您的API密钥：

```bash
# 必需：至少一个AI模型API密钥
DASHSCOPE_API_KEY=your_dashscope_api_key_here    # 阿里百炼(推荐)
DEEPSEEK_API_KEY=your_deepseek_api_key_here      # DeepSeek V3(推荐)

# 推荐：数据源API密钥
FINNHUB_API_KEY=your_finnhub_api_key_here        # 美股数据
TUSHARE_TOKEN=your_tushare_token_here            # A股数据
```

#### 步骤2: 安装依赖
```bash
pip install -r requirements.txt
```

#### 步骤3: 验证配置
```bash
python check_config.py
```

#### 步骤4: 启动应用
```bash
python start_web.py
```

#### 步骤5: 访问应用
打开浏览器访问: http://localhost:8501

## 🔑 API密钥获取

### 🇨🇳 国产AI模型 (推荐)

**阿里百炼 (DashScope)**
- 网址: https://dashscope.aliyun.com/
- 步骤: 注册阿里云账号 → 开通百炼服务 → 获取API密钥
- 优势: 中文优化，稳定可靠

**DeepSeek V3**
- 网址: https://platform.deepseek.com/
- 步骤: 注册账号 → 控制台 → API Keys → 创建密钥
- 优势: 性价比极高，功能强大

### 🌍 国外AI模型

**OpenAI**
- 网址: https://platform.openai.com/
- 需要: 国外网络环境

**Google AI**
- 网址: https://ai.google.dev/
- 优势: 免费额度较大

### 📊 数据源API

**FinnHub (美股数据)**
- 网址: https://finnhub.io/
- 免费: 每分钟60次请求

**Tushare (A股数据)**
- 网址: https://tushare.pro/
- 注册: 邮箱验证后获取Token

## 🐳 Docker部署 (可选)

如果您想使用Docker部署：

```bash
# 1. 修改.env文件中的数据库配置
MONGODB_ENABLED=true
REDIS_ENABLED=true
MONGODB_HOST=mongodb
REDIS_HOST=redis

# 2. 启动服务
docker-compose up -d

# 3. 访问应用
# Web界面: http://localhost:8501
# MongoDB管理: http://localhost:8081
# Redis管理: http://localhost:8082
```

## 📋 功能特性

- 🤖 **多智能体协作**: 基本面、技术面、新闻面、社交媒体分析师
- 🇨🇳 **中文优化**: 完整的A股/港股/美股支持
- 🧠 **多LLM支持**: 支持4大提供商，60+模型
- 📊 **实时分析**: 5级研究深度，2-25分钟完成分析
- 📄 **专业报告**: 支持Word/PDF/Markdown格式导出
- 🎯 **智能决策**: 买入/持有/卖出建议，置信度评估

## ❓ 常见问题

**Q: 必须配置哪些API密钥？**
A: 至少需要一个AI模型API密钥，推荐DeepSeek或阿里百炼。

**Q: 如何获得更好的分析效果？**
A: 建议同时配置Tushare Token(A股数据)和FinnHub API Key(美股数据)。

**Q: 支持哪些股票市场？**
A: 支持A股(如000001)、美股(如AAPL)、港股(如0700.HK)。

**Q: 分析一次需要多长时间？**
A: 根据研究深度，从2分钟(快速)到25分钟(全面分析)不等。

## 📚 更多资源

- 📖 [完整文档](docs/)
- 🆘 [常见问题](docs/faq/faq.md)
- 🚀 [快速开始](docs/overview/quick-start.md)
- 🏛️ [系统架构](docs/architecture/system-architecture.md)

## 🆘 获取帮助

- GitHub Issues: https://github.com/hsliuping/TradingAgents-CN/issues
- 邮箱: <EMAIL>
- QQ群: 782124367

---

🌟 **如果这个项目对您有帮助，请给我们一个 Star！**
