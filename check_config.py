#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradingAgents-CN 配置检查工具
用于验证环境配置是否正确
"""

import os
import sys
from pathlib import Path

def check_env_file():
    """检查.env文件是否存在"""
    env_path = Path('.env')
    if not env_path.exists():
        print("❌ .env文件不存在")
        print("💡 请复制.env.example为.env并填入您的API密钥")
        return False
    
    print("✅ .env文件存在")
    return True

def load_env_file():
    """简单的.env文件加载"""
    env_vars = {}
    env_path = Path('.env')
    if env_path.exists():
        try:
            with open(env_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key.strip()] = value.strip()
        except Exception as e:
            print(f"读取.env文件失败: {e}")
    return env_vars

def check_api_keys():
    """检查API密钥配置"""
    env_vars = load_env_file()
    
    # 必需的API密钥（至少需要一个AI模型API密钥）
    ai_keys = {
        'DASHSCOPE_API_KEY': '阿里百炼',
        'DEEPSEEK_API_KEY': 'DeepSeek V3',
        'OPENAI_API_KEY': 'OpenAI',
        'GOOGLE_API_KEY': 'Google AI',
        'ANTHROPIC_API_KEY': 'Anthropic Claude',
        'OPENROUTER_API_KEY': 'OpenRouter'
    }
    
    # 数据源API密钥
    data_keys = {
        'FINNHUB_API_KEY': 'FinnHub (美股数据)',
        'TUSHARE_TOKEN': 'Tushare (A股数据)'
    }
    
    print("\n🤖 AI模型API密钥检查:")
    ai_configured = False
    for key, name in ai_keys.items():
        value = env_vars.get(key, '')
        if value and value != f'your_{key.lower()}_here':
            print(f"✅ {name}: 已配置")
            ai_configured = True
        else:
            print(f"❌ {name}: 未配置")
    
    if not ai_configured:
        print("⚠️  警告: 没有配置任何AI模型API密钥，系统无法正常工作")
        print("💡 建议: 至少配置一个AI模型API密钥 (推荐DeepSeek或阿里百炼)")
    
    print("\n📊 数据源API密钥检查:")
    for key, name in data_keys.items():
        value = env_vars.get(key, '')
        if value and value != f'your_{key.lower()}_here':
            print(f"✅ {name}: 已配置")
        else:
            print(f"❌ {name}: 未配置")
    
    return ai_configured

def check_directories():
    """检查必要的目录结构"""
    required_dirs = [
        'data',
        'data/cache',
        'data/reports',
        'results',
        'cache',
        'logs'
    ]
    
    print("\n📁 目录结构检查:")
    all_exist = True
    for dir_path in required_dirs:
        path = Path(dir_path)
        if path.exists():
            print(f"✅ {dir_path}: 存在")
        else:
            print(f"❌ {dir_path}: 不存在")
            try:
                path.mkdir(parents=True, exist_ok=True)
                print(f"✅ {dir_path}: 已创建")
            except Exception as e:
                print(f"❌ {dir_path}: 创建失败 - {e}")
                all_exist = False
    
    return all_exist

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"\n🐍 Python版本检查:")
    print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 10:
        print("✅ Python版本符合要求 (3.10+)")
        return True
    else:
        print("❌ Python版本过低，需要3.10或更高版本")
        return False

def check_dependencies():
    """检查关键依赖包"""
    required_packages = [
        'openai',
        'langchain',
        'streamlit',
        'pandas',
        'requests',
        'python-dotenv'
    ]
    
    print(f"\n📦 依赖包检查:")
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 安装缺失的依赖包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔍 TradingAgents-CN 配置检查工具")
    print("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("环境文件", check_env_file),
        ("API密钥", check_api_keys),
        ("目录结构", check_directories),
        ("依赖包", check_dependencies)
    ]
    
    all_passed = True
    for name, check_func in checks:
        try:
            result = check_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {name}检查失败: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过！系统配置正确")
        print("💡 下一步: 运行 python start_web.py 启动Web界面")
    else:
        print("⚠️  部分检查未通过，请根据上述提示进行配置")
        print("📚 详细配置说明请参考: README.md")

if __name__ == "__main__":
    main()
